import React, { useState, useEffect } from 'react';
import {
  markTransferAsSuccessful,
  markTransferAsFailed,
  markTransferAsReturned,
  cancelTransfer,
  getTransferStatus,
  getAvailableFailureReasons,
  simulateCommonScenarios,
  isSandboxEnvironment,
  FailureReason,
  SandboxTransferControl
} from '../services/plaidSandbox';

const PlaidSandboxTester: React.FC = () => {
  const [transferId, setTransferId] = useState('');
  const [delay, setDelay] = useState(0);
  const [customReason, setCustomReason] = useState('');
  const [selectedFailureReason, setSelectedFailureReason] = useState('');
  const [failureReasons, setFailureReasons] = useState<FailureReason[]>([]);
  const [result, setResult] = useState<SandboxTransferControl | null>(null);
  const [loading, setLoading] = useState(false);
  const [transferStatus, setTransferStatus] = useState<any>(null);

  // Check if sandbox environment is available
  const sandboxAvailable = isSandboxEnvironment();

  useEffect(() => {
    if (sandboxAvailable) {
      loadFailureReasons();
    }
  }, [sandboxAvailable]);

  const loadFailureReasons = async () => {
    try {
      const response = await getAvailableFailureReasons();
      if (response.success && response.failureReasons) {
        setFailureReasons(response.failureReasons);
        if (response.failureReasons.length > 0) {
          setSelectedFailureReason(response.failureReasons[0].value);
        }
      }
    } catch (error) {
      console.error('Error loading failure reasons:', error);
    }
  };

  const handleAction = async (action: () => Promise<SandboxTransferControl>) => {
    if (!transferId.trim()) {
      setResult({
        success: false,
        message: 'Please enter a transfer ID'
      });
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      const response = await action();
      setResult(response);
    } catch (error) {
      setResult({
        success: false,
        message: 'An error occurred while processing the request'
      });
    } finally {
      setLoading(false);
    }
  };

  const checkTransferStatus = async () => {
    if (!transferId.trim()) {
      setTransferStatus({
        success: false,
        message: 'Please enter a transfer ID'
      });
      return;
    }

    setLoading(true);
    try {
      const response = await getTransferStatus(transferId);
      setTransferStatus(response);
    } catch (error) {
      setTransferStatus({
        success: false,
        message: 'An error occurred while checking transfer status'
      });
    } finally {
      setLoading(false);
    }
  };

  if (!sandboxAvailable) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Sandbox Testing Not Available
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                Plaid sandbox testing features are only available in sandbox environment.
                Current environment: {import.meta.env.VITE_PLAID_ENV || 'production'}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Plaid Sandbox Transfer Tester
        </h2>
        <p className="text-gray-600">
          Manually control transfer outcomes for testing purposes (Sandbox only)
        </p>
      </div>

      {/* Transfer ID Input */}
      <div className="mb-6">
        <label htmlFor="transferId" className="block text-sm font-medium text-gray-700 mb-2">
          Transfer ID
        </label>
        <input
          type="text"
          id="transferId"
          value={transferId}
          onChange={(e) => setTransferId(e.target.value)}
          placeholder="Enter Plaid transfer ID"
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Delay Input */}
      <div className="mb-6">
        <label htmlFor="delay" className="block text-sm font-medium text-gray-700 mb-2">
          Delay (milliseconds)
        </label>
        <input
          type="number"
          id="delay"
          value={delay}
          onChange={(e) => setDelay(Number(e.target.value))}
          placeholder="0"
          min="0"
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <button
          onClick={() => handleAction(() => markTransferAsSuccessful(transferId, delay))}
          disabled={loading}
          className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50"
        >
          Mark as Successful
        </button>

        <button
          onClick={() => handleAction(() => markTransferAsReturned(transferId, customReason || 'Account closed', delay))}
          disabled={loading}
          className="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50"
        >
          Mark as Returned
        </button>

        <button
          onClick={() => handleAction(() => cancelTransfer(transferId, delay))}
          disabled={loading}
          className="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50"
        >
          Cancel Transfer
        </button>
      </div>

      {/* Failure Reason Selection */}
      <div className="mb-4">
        <label htmlFor="failureReason" className="block text-sm font-medium text-gray-700 mb-2">
          Failure Reason
        </label>
        <select
          id="failureReason"
          value={selectedFailureReason}
          onChange={(e) => setSelectedFailureReason(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          {failureReasons.map((reason) => (
            <option key={reason.value} value={reason.value}>
              {reason.description}
            </option>
          ))}
        </select>
      </div>

      {/* Custom Reason Input */}
      <div className="mb-6">
        <label htmlFor="customReason" className="block text-sm font-medium text-gray-700 mb-2">
          Custom Reason (optional)
        </label>
        <input
          type="text"
          id="customReason"
          value={customReason}
          onChange={(e) => setCustomReason(e.target.value)}
          placeholder="Enter custom failure/return reason"
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Mark as Failed Button */}
      <div className="mb-6">
        <button
          onClick={() => handleAction(() => markTransferAsFailed(transferId, selectedFailureReason, customReason, delay))}
          disabled={loading}
          className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50 w-full"
        >
          Mark as Failed
        </button>
      </div>

      {/* Check Status Button */}
      <div className="mb-6">
        <button
          onClick={checkTransferStatus}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50 w-full"
        >
          Check Transfer Status
        </button>
      </div>

      {/* Quick Actions */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Quick Test Scenarios</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => handleAction(() => simulateCommonScenarios.insufficientFunds(transferId, delay))}
            disabled={loading}
            className="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50"
          >
            Insufficient Funds
          </button>
          <button
            onClick={() => handleAction(() => simulateCommonScenarios.accountClosed(transferId, delay))}
            disabled={loading}
            className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50"
          >
            Account Closed
          </button>
          <button
            onClick={() => handleAction(() => simulateCommonScenarios.delayedSuccess(transferId, 5000))}
            disabled={loading}
            className="bg-teal-600 hover:bg-teal-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50"
          >
            Delayed Success (5s)
          </button>
          <button
            onClick={() => handleAction(() => simulateCommonScenarios.returnedTransfer(transferId, delay))}
            disabled={loading}
            className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50"
          >
            Returned Transfer
          </button>
        </div>
      </div>

      {/* Results */}
      {result && (
        <div className={`p-4 rounded-md mb-4 ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
          <h3 className={`text-lg font-medium ${result.success ? 'text-green-800' : 'text-red-800'}`}>
            {result.success ? 'Success' : 'Error'}
          </h3>
          <p className={`mt-1 ${result.success ? 'text-green-700' : 'text-red-700'}`}>
            {result.message}
          </p>
          {result.transferId && (
            <p className={`mt-1 text-sm ${result.success ? 'text-green-600' : 'text-red-600'}`}>
              Transfer ID: {result.transferId}
            </p>
          )}
          {result.status && (
            <p className={`mt-1 text-sm ${result.success ? 'text-green-600' : 'text-red-600'}`}>
              Status: {result.status}
            </p>
          )}
        </div>
      )}

      {/* Transfer Status */}
      {transferStatus && (
        <div className={`p-4 rounded-md ${transferStatus.success ? 'bg-blue-50 border border-blue-200' : 'bg-red-50 border border-red-200'}`}>
          <h3 className={`text-lg font-medium ${transferStatus.success ? 'text-blue-800' : 'text-red-800'}`}>
            Transfer Status
          </h3>
          {transferStatus.success ? (
            <div className="mt-2 text-blue-700">
              <p><strong>Status:</strong> {transferStatus.status}</p>
              {transferStatus.failureReason && (
                <p><strong>Failure Reason:</strong> {transferStatus.failureReason}</p>
              )}
            </div>
          ) : (
            <p className="mt-1 text-red-700">{transferStatus.message}</p>
          )}
        </div>
      )}

      {loading && (
        <div className="text-center py-4">
          <div className="inline-flex items-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          </div>
        </div>
      )}
    </div>
  );
};

export default PlaidSandboxTester;
